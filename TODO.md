fix ┏━━━━━━━┓
┗ border for heading 1 

fix broken markdown display with inline code tags in lists

refactor src/colors.rs:107 into my colors.rs

always ensure newline before and after entire list tags

refactor those files, first run find wc and refactor all above 300 lines
591 ./src/handler/tool_executor.rs
587 ./src/display/ai_message.rs
403 ./src/llm_client/tools.rs
309 ./src/app.rs

is there any way to display 'ctrl+c to interrupt' in gray, while AI is thinking below the prompt, AND also remove it
once AI has generated
response?

while in this, ctrl-c or ctrl-d do also not work yet
API call failed with error: API error: API request failed with status 530 <unknown status code>: error code: 1033.
Retrying in 200ms...

API call failed with error: API error: API request failed with status 530 <unknown status code>: error code: 1033.
Retrying in 400ms...

API call failed with error: API error: API request failed with status 530 <unknown status code>: error code: 1033.
Retrying in 800ms...

API call failed with error: API error: API request failed with status 530 <unknown status code>: error code: 1033.
Retrying in 1.6s...

API call failed with error: API error: API request failed with status 530 <unknown status code>: error code: 1033.
Retrying in 3.2s...

API call failed with error: API error: API request failed with status 530 <unknown status code>: error code: 1033.
Retrying in 6.4s...

API call failed with error: API error: API request failed with status 530 <unknown status code>: error code: 1033.
Retrying in 12.8s...

API call failed with error: API error: API request failed with status 530 <unknown status code>: error code: 1033.
Retrying in 25.6s...

maybe use a global boolean for that, set in handler.rs depending on if AI is thinking or not
ctrl-c while AI is thinking should abort the AI response
then check that in suspendable_editor.rs and somehow send an event back to abort the network request

use transient prompt to make prompt disappear, check example:
cargo run --example transient_prompt

use this by default, or is it already used?
    #[arg(long, value_name = "TOKEN_LIMIT", help = "Inject workspace files into system prompt with token limit (default: 1000)")]
    pub inject_workspace: Option<Option<usize>>,


TEST this from AI
inject workspace files into system prompt, limit to say 1k tokens, example:
Here is the structure of the current workspace:
<workspace>
build.gradle.kts
.gitignore
.goosehints
gradle.properties
gradlew
gradlew.bat
README.md
settings.gradle.kts
SingleLineTextFieldSpec.md
TextEditorImplementationSpec.md

composeApp/:
alert.mp3
build.gradle.kts

gradle/:
libs.versions.toml

gradle//wrapper/:
gradle-wrapper.jar
gradle-wrapper.properties

memory-bank/:
techContext.md
</workspace>

needs runtime model change capabilities
add gemini 2.0 flash provider ((has tools))
add cohere provider (has tools)
