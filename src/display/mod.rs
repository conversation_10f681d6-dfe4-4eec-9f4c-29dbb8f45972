// Re-export all display functionality
pub mod ai_message;
pub mod common;
pub mod system_message;
pub mod user_message;
mod colors;

use crate::app::AppMessage;
pub use common::truncate_tool_output;
use std::io;

use reedline::ExternalPrinter;

pub fn print_formatted_message(app_msg: &AppMessage) -> io::Result<()> {
    match app_msg.sender.as_str() {
        "User" => user_message::print_user_message(app_msg),
        "AI" => ai_message::print_ai_message(app_msg),
        _ => system_message::print_system_message(app_msg),
    }
}

pub fn print_formatted_message_external(
    app_msg: &AppMessage,
    external_printer: &ExternalPrinter<String>,
) -> Result<(), Box<dyn std::error::Error>> {
    // For now, just use a simple text representation
    // TODO: Implement proper formatting for external printer
    let simple_message = match app_msg.sender.as_str() {
        "User" => {
            if let Some(crate::app::MessageContent::Text(text)) = app_msg.parts.first() {
                format!("╭─ User ─╮\n│ {} │\n╰────────╯", text)
            } else {
                "User: [Non-text content]".to_string()
            }
        }
        "AI" => {
            let mut content = String::new();
            for part in &app_msg.parts {
                match part {
                    crate::app::MessageContent::Text(text) => {
                        content.push_str(text);
                        content.push('\n');
                    }
                    _ => {
                        content.push_str("[Non-text content]\n");
                    }
                }
            }
            format!("AI: {}", content)
        }
        _ => {
            format!("{}: {:?}", app_msg.sender, app_msg.parts)
        }
    };

    // Send the formatted message through the external printer
    external_printer.print(simple_message)?;

    Ok(())
}


